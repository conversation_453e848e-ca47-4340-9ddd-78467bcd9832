// src/app/student/assignments/page.tsx
"use client";

import { api } from "@/utils/trpc-provider";
import Link from "next/link";
import { ArrowRightIcon, BookOpenIcon } from "@heroicons/react/24/outline";

type TemplateAssignment = {
  id: string;
  title: string;
  description: string;
  target_standard: string;
  gradeLevel: number;
  subject: string;
  questionCount: number;
  createdAt: string;
};

const AssignmentCard = ({ assignment }: { assignment: TemplateAssignment }) => {
  return (
    <Link href={`/student/assignments/${assignment.id}`} passHref>
      <div className="block p-6 bg-white rounded-xl border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer hover:-translate-y-1">
        <div className="flex items-center mb-4">
          <div className="p-2 bg-blue-100 rounded-full mr-4">
            <BookOpenIcon className="h-7 w-7 text-blue-600" />
          </div>
          <h2 className="text-xl font-semibold tracking-tight text-gray-800">
            {assignment.title}
          </h2>
        </div>
        <p className="font-normal text-gray-600 mb-5 line-clamp-2">
          {assignment.description}
        </p>
        <div className="flex justify-between items-center text-sm">
          <div className="text-gray-500">
            <div>
              {assignment.questionCount} Questions • Grade{" "}
              {assignment.gradeLevel}
            </div>
            <div className="text-xs mt-1 text-blue-600">
              Standard: {assignment.target_standard.replace("_", " & ")}
            </div>
          </div>
          <div className="flex items-center text-blue-600 font-medium group">
            Start Assignment
            <ArrowRightIcon className="h-4 w-4 ml-1.5 transition-transform group-hover:translate-x-1" />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default function AssignmentsPage() {
  const {
    data: assignments,
    isLoading,
    error,
  } = api.chat.getTemplateAssignments.useQuery();

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading assignments...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="text-center py-12 bg-red-50 rounded-xl border border-red-200">
          <p className="text-red-600">
            Error loading assignments: {error.message}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Your Assignments
        </h1>
        <p className="text-gray-600">
          Complete your personalized assignments to track your learning
          progress.
        </p>
      </div>

      {/* Assignments Grid */}
      {assignments && assignments.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {assignments.map((assignment) => (
            <AssignmentCard key={assignment.id} assignment={assignment} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-xl shadow-sm border border-gray-200">
          <BookOpenIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <p className="text-xl text-gray-600">
            No assignments available at the moment.
          </p>
          <p className="text-gray-500 mt-1">Please check back later.</p>
        </div>
      )}
    </div>
  );
}
