"use client";
import React, { useEffect, useState, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import ProtectedRoute from "@/components/protected-route";
import { voteOnAIResponse } from "@/app/actions/vote-on-ai-response";
import type {
  ChatMessage,
  StudentAssignmentAnswers,
} from "../../../../types/assignments";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import { api } from "@/utils/trpc-provider";
import {
  HomeIcon,
  CheckIcon,
  SparklesIcon,
  Bars3Icon,
  ArrowUpIcon,
  ArrowRightIcon,
  HandThumbDownIcon as HandThumbsDownFilled,
  HandThumbUpIcon as HandThumbsUpFilled,
} from "@heroicons/react/24/solid";

import {
  HandThumbDownIcon,
  HandThumbUpIcon,
} from "@heroicons/react/24/outline";

const thumbClass = "h-4 w-4 text-blue-800 hover:text-blue-950 cursor-pointer";

function MessageComponent({ msg, firstInitial }: MessageComponentProps) {
  const [vote, setVote] = useState<boolean | null>(
    msg.student_vote !== undefined ? msg.student_vote : null
  );
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [description, setDescription] = useState<string>("");

  function upvote() {
    setVote(true);
    voteOnAIResponse(msg.id, true, null);
  }

  function downvote() {
    setShowPopup(true);
  }

  function submitDownvote() {
    setShowPopup(false);
    setVote(false);
    voteOnAIResponse(msg.id, false, description);
    setDescription("");
  }

  function undoVote() {
    setVote(null);
    voteOnAIResponse(msg.id, null, null);
  }

  return (
    <div
      className={`relative flex items-center gap-3 ${
        msg.sender === "student" ? "justify-end" : ""
      }`}
    >
      {msg.sender === "EDEN" && showPopup && (
        <div className="flex flex-col absolute bottom-0 left-0 bg-gray-100/95 rounded-lg px-4 py-3 items-center">
          <input
            placeholder="What is unhelpful about this response?"
            className="w-xs mb-2"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
          />
          <button
            onClick={submitDownvote}
            className="bg-sky-100 py-1 px-2 rounded-full hover:bg-sky-300 cursor-pointer"
          >
            Submit feedback
          </button>
        </div>
      )}
      {msg.sender === "EDEN" && (
        <div className="flex flex-col">
          <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0">
            <SparklesIcon className="h-4 w-4 text-white" />
          </div>
          <div className="flex w-full justify-center">
            {vote === true ? (
              <HandThumbsUpFilled className={thumbClass} onClick={undoVote} />
            ) : (
              <HandThumbUpIcon className={thumbClass} onClick={upvote} />
            )}
            {vote === false ? (
              <HandThumbsDownFilled className={thumbClass} onClick={undoVote} />
            ) : (
              <HandThumbDownIcon className={thumbClass} onClick={downvote} />
            )}
          </div>
        </div>
      )}

      <div
        className={`px-4 py-2 rounded-lg max-w-[70%] ${
          msg.sender === "EDEN"
            ? "bg-gray-100 text-gray-800"
            : msg.sender === "student"
            ? msg.stepId
              ? "bg-green-600 text-white"
              : "bg-blue-600 text-white"
            : "bg-gray-50 text-gray-700"
        }`}
      >
        {msg.text}
      </div>
      {msg.sender === "student" && (
        <div className="w-8 h-8 rounded-full bg-orange-400 text-white flex items-center justify-center flex-shrink-0 text-sm font-medium">
          {/*can add an avatar too */}
          {firstInitial || "S"}{" "}
        </div>
      )}
    </div>
  );
}

// Interface for database problem
interface DatabaseProblem {
  id: string;
  title: string;
  statement: string;
  problem_type: string;
  standard_code: string[];
  expected_steps: string[];
  correct_answer: string;
  microservice_payload: {
    statement: string;
    answer: string;
    steps: string[];
  };
  sequence_order?: number;
}

// Interface for database assignment
interface DatabaseAssignment {
  id: string;
  title: string;
  description: string;
  target_standard: string;
  problems: DatabaseProblem[];
  studentAssignmentId: string;
  status: string;
}

// Interface for session
interface Session {
  id: string;
  user_id: string;
  problem_id: string;
  created_at: string;
}

// Props interfaces
interface MessageComponentProps {
  msg: ChatMessage;
  firstInitial?: string;
}

export default function AssignmentInstancePage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const assignmentId = params.assignmentId as string;
  const [assignment, setAssignment] = useState<DatabaseAssignment | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentStepInQuestion, setCurrentStepInQuestion] = useState(0);
  const [studentAnswers, setStudentAnswers] =
    useState<StudentAssignmentAnswers>({});
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState("");
  const [chatLoading, setChatLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showStepSidebar, setShowStepSidebar] = useState<boolean>(true);

  const [activeProblem, setActiveProblem] = useState<DatabaseProblem | null>(
    null
  );
  const [currentSession, setCurrentSession] = useState<Session | null>(null);
  const [finalAnswer, setFinalAnswer] = useState("");
  const [answerValidation, setAnswerValidation] = useState<{
    isCorrect: boolean | null;
    message: string;
  }>({ isCorrect: null, message: "" });
  const [isGeneratingAssignment, setIsGeneratingAssignment] = useState(false);
  const chatEndRef = useRef<HTMLDivElement>(null);
  const sessionCreatedRef = useRef(false);
  const assignmentSetupRef = useRef(false);
  const generationAttemptedRef = useRef(false);

  // tRPC queries and mutations
  const generateAssignmentMutation =
    api.chat.generateStudentAssignment.useMutation();

  const {
    data: studentAssignment,
    isLoading: isLoadingAssignment,
    error: assignmentError,
  } = api.chat.getStudentAssignment.useQuery(
    { templateAssignmentId: assignmentId },
    {
      enabled: !!assignmentId && !isGeneratingAssignment,
      retry: false, // Don't retry if assignment doesn't exist yet
    }
  );

  const createSessionMutation = api.chat.getOrCreateSession.useMutation({
    onSuccess: (session) => {
      setCurrentSession(session);
      refetchChatHistory();
    },
  });

  const sendChatMutation = api.chat.sendChatMessage.useMutation({
    onSuccess: async (data) => {
      console.log("Chat message sent successfully:", data);
      setChatInput("");
      // Add a small delay to ensure database consistency before refetching
      setTimeout(() => {
        refetchChatHistory();
      }, 500);
    },
    onError: (error) => {
      console.error("Error sending chat message:", error);
    },
  });

  const { data: chatHistoryData, refetch: refetchChatHistory } =
    api.chat.getChatHistory.useQuery(
      {
        sessionId: currentSession?.id || "",
        problemId: activeProblem?.id,
      },
      { enabled: !!currentSession?.id }
    );

  // Convert database chat messages to UI format
  useEffect(() => {
    if (chatHistoryData) {
      console.log("Chat history updated:", chatHistoryData);
      const convertedMessages: ChatMessage[] = chatHistoryData.map(
        (msg: {
          id: string;
          sender_type: string;
          content: string;
          timestamp: string;
          student_vote: boolean | null;
        }) => ({
          id: msg.id,
          sender: msg.sender_type === "student" ? "student" : "EDEN",
          text: msg.content,
          timestamp: new Date(msg.timestamp),
          student_vote: msg.student_vote,
        })
      );
      setChatMessages(convertedMessages);
    }
  }, [chatHistoryData]);

  // Handle assignment generation when it doesn't exist
  useEffect(() => {
    const generateAssignment = async () => {
      if (
        !assignmentId ||
        !assignmentError ||
        isGeneratingAssignment ||
        generationAttemptedRef.current
      )
        return;

      generationAttemptedRef.current = true;
      setIsGeneratingAssignment(true);
      try {
        await generateAssignmentMutation.mutateAsync({
          templateAssignmentId: assignmentId,
        });
        // Refetch will happen automatically due to query invalidation
      } catch (error) {
        console.error("Error generating assignment:", error);
        router.push("/student/assignments");
      } finally {
        setIsGeneratingAssignment(false);
      }
    };

    generateAssignment();
  }, [
    assignmentId,
    assignmentError,
    isGeneratingAssignment,
    generateAssignmentMutation,
    router,
  ]);

  // Handle assignment data setup when loaded
  useEffect(() => {
    if (!studentAssignment || assignmentSetupRef.current) return;

    assignmentSetupRef.current = true;
    setAssignment(studentAssignment);

    // Initialize student answers structure
    const answers: StudentAssignmentAnswers = {};
    studentAssignment.problems.forEach((problem) => {
      answers[problem.id] = { steps: {}, final: {} };
      // For mechanical problems, initialize step answers
      if (
        problem.problem_type === "problem_mechanical" &&
        problem.expected_steps
      ) {
        problem.expected_steps.forEach((_step: string, index: number) => {
          answers[problem.id].steps[`step_${index}`] = "";
        });
      }
      // Initialize final answer
      answers[problem.id].final["answer"] = "";
    });
    setStudentAnswers(answers);

    // Set current problem to first problem
    if (studentAssignment.problems.length > 0) {
      setActiveProblem(studentAssignment.problems[0]);
      setCurrentQuestionIndex(0);
      setCurrentStepInQuestion(0);

      // Create session for the first problem
      if (!sessionCreatedRef.current) {
        sessionCreatedRef.current = true;
        createSessionMutation.mutate({
          problemId: studentAssignment.problems[0].id,
        });
      }
    }
  }, [studentAssignment, createSessionMutation]);

  // Reset refs when assignmentId changes
  useEffect(() => {
    sessionCreatedRef.current = false;
    assignmentSetupRef.current = false;
    generationAttemptedRef.current = false;
  }, [assignmentId]);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatMessages]);

  if (!assignment || isLoadingAssignment) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-16 w-16 animate-spin rounded-full border-4 border-gray-200 border-t-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {isGeneratingAssignment
              ? "Generating your personalized assignment..."
              : "Loading assignment..."}
          </p>
        </div>
      </div>
    );
  }

  const problems = assignment.problems;
  const totalQuestions = problems.length;
  const currentProblem = problems[currentQuestionIndex];

  // For mechanical problems, we have steps; for socratic, we don't
  const steps = currentProblem?.expected_steps || [];
  const isFinalAnswerStage = currentStepInQuestion >= steps.length;
  // Hide step sidebar for socratic problems
  const shouldShowStepSidebar =
    currentProblem?.problem_type === "problem_mechanical" && showStepSidebar;

  //const processedProblemStatement = currentQ.problemStatement
  // .replace(/{vars\[0\]}/g, currentQ.variables[0])
  // .replace(/{vars\[1\]}/g, currentQ.variables[1]);

  const quickPrompts = [
    { icon: "🤔", text: "Where do I start?" },
    { icon: "🤷", text: "I'm stuck on this step" },
    { icon: "💡", text: "Can you give me a hint?" },
    { icon: "🔍", text: "Explain this concept" },
  ];

  async function handleChatSend(userMsg: string) {
    if (!userMsg.trim() || !currentSession || !activeProblem) return;

    setChatLoading(true);

    // Add user message optimistically
    const tempUserMessage: ChatMessage = {
      id: `temp-user-${Date.now()}`,
      sender: "student",
      text: userMsg.trim(),
      timestamp: new Date(),
    };
    setChatMessages((prev) => [...prev, tempUserMessage]);
    setChatInput("");

    try {
      await sendChatMutation.mutateAsync({
        sessionId: currentSession.id,
        problemId: activeProblem.id,
        userMessage: userMsg.trim(),
      });
    } catch (error) {
      console.error("Chat error:", error);
      // Add error message
      setChatMessages((prev) => [
        ...prev,
        {
          id: `error-${Date.now()}`,
          sender: "EDEN",
          text: "Sorry, I'm having trouble connecting. Please try again.",
          timestamp: new Date(),
        },
      ]);
    }

    setChatLoading(false);
  }

  function handleFinalChange(varname: string, value: string) {
    if (!currentProblem) return;
    setStudentAnswers((prev) => ({
      ...prev,
      [currentProblem.id]: {
        ...prev[currentProblem.id],
        final: { ...prev[currentProblem.id].final, [varname]: value },
      },
    }));
  }

  function handleAnswerValidation() {
    if (!activeProblem || !finalAnswer.trim()) {
      setAnswerValidation({
        isCorrect: null,
        message: "Please enter an answer to validate.",
      });
      return;
    }

    // Get expected answer from database
    const expectedAnswer =
      activeProblem.correct_answer ||
      activeProblem.microservice_payload?.answer;

    if (!expectedAnswer) {
      setAnswerValidation({
        isCorrect: null,
        message: "Unable to validate answer - no expected answer found.",
      });
      return;
    }

    const studentAnswer = finalAnswer.trim();
    const isCorrect = validateAnswer(studentAnswer, expectedAnswer);

    setAnswerValidation({
      isCorrect,
      message: isCorrect
        ? "Correct! Well done!"
        : "Incorrect. Please try again.",
    });

    // Add validation result to chat
    setChatMessages((prev) => [
      ...prev,
      {
        id: `validation-${Date.now()}`,
        sender: "EDEN",
        text: isCorrect
          ? "🎉 Excellent work! Your answer is correct!"
          : "That's not quite right. Please try again or ask for help if you need it.",
        timestamp: new Date(),
      },
    ]);
  }

  function parseExpectedAnswer(
    expectedAnswer: string | object | null | undefined
  ): { variable: string; value: string } | null {
    if (!expectedAnswer) return null;

    if (typeof expectedAnswer === "string") {
      // Handle string formats like "x=5", "x:5", or just "5"
      if (expectedAnswer.includes("=")) {
        const [variable, value] = expectedAnswer
          .split("=")
          .map((s) => s.trim());
        return { variable, value };
      } else if (expectedAnswer.includes(":")) {
        const [variable, value] = expectedAnswer
          .split(":")
          .map((s) => s.trim());
        return { variable, value };
      } else {
        // Just a value, assume variable is 'x'
        return { variable: "x", value: expectedAnswer.trim() };
      }
    } else if (typeof expectedAnswer === "object" && expectedAnswer !== null) {
      // Handle object formats like { x: "5" } or { answer: "x=5" }
      if (
        "answer" in expectedAnswer &&
        typeof expectedAnswer.answer === "string"
      ) {
        return parseExpectedAnswer(expectedAnswer.answer);
      } else if (
        "value" in expectedAnswer &&
        typeof expectedAnswer.value === "string"
      ) {
        return parseExpectedAnswer(expectedAnswer.value);
      } else {
        // Try to extract variable and value from object properties
        const entries = Object.entries(expectedAnswer);
        if (entries.length === 1) {
          const [variable, value] = entries[0];
          return { variable, value: String(value) };
        }
      }
    }

    return null;
  }

  function normalizeStudentAnswer(
    studentAnswer: string,
    expectedVariable: string
  ): string[] {
    const normalized = studentAnswer.replace(/\s/g, "").toLowerCase();

    // Generate possible valid formats the student might enter
    const possibleFormats = [
      normalized, // Raw input: "5"
      `${expectedVariable}=${normalized}`, // Variable format: "x=5"
      `${expectedVariable}:${normalized}`, // Colon format: "x:5"
    ];

    // Also handle if student included the variable
    if (normalized.includes("=")) {
      const [, value] = normalized.split("=");
      possibleFormats.push(value); // Extract just the value part
    }
    if (normalized.includes(":")) {
      const [, value] = normalized.split(":");
      possibleFormats.push(value); // Extract just the value part
    }

    return possibleFormats;
  }

  function validateAnswer(
    studentAnswer: string,
    expectedAnswer: string | object | null | undefined
  ): boolean {
    const parsedExpected = parseExpectedAnswer(expectedAnswer);
    if (!parsedExpected) return false;

    const { variable, value } = parsedExpected;
    const normalizedExpectedValue = value.replace(/\s/g, "").toLowerCase();

    // Get all possible formats the student might have entered
    const studentFormats = normalizeStudentAnswer(studentAnswer, variable);

    // Check if any of the student formats match the expected value
    return studentFormats.some((format) => format === normalizedExpectedValue);
  }

  function handleFinalSubmit() {
    if (!currentProblem) return;

    const studentAnswer =
      studentAnswers[currentProblem.id]?.final["answer"]?.trim();

    // Check if final answer is provided
    if (!studentAnswer) {
      setChatMessages((msgs) => [
        ...msgs,
        {
          id: `${Date.now()}_${Math.random()}`,
          sender: "EDEN",
          text: "Please provide your final answer before continuing.",
          timestamp: new Date(),
        },
      ]);
      return;
    }

    // Get expected answer from database
    const expectedAnswer =
      currentProblem.correct_answer ||
      currentProblem.microservice_payload?.answer;

    if (!expectedAnswer) {
      console.error("No expected answer found for problem:", currentProblem.id);
      setChatMessages((msgs) => [
        ...msgs,
        {
          id: `${Date.now()}_${Math.random()}`,
          sender: "EDEN",
          text: "There seems to be an issue with this problem. Please contact your teacher.",
          timestamp: new Date(),
        },
      ]);
      return;
    }

    // Validate the answer
    const isCorrect = validateAnswer(studentAnswer, expectedAnswer);

    if (!isCorrect) {
      // Show incorrect answer feedback
      setChatMessages((msgs) => [
        ...msgs,
        {
          id: `${Date.now()}_${Math.random()}`,
          sender: "EDEN",
          text: "That's not quite right. Please try again or ask for help if you need it.",
          timestamp: new Date(),
        },
      ]);

      // Update validation state for UI feedback
      setAnswerValidation({
        isCorrect: false,
        message: "Incorrect. Please try again.",
      });
      return;
    }

    // Answer is correct - show success and proceed
    setChatMessages((msgs) => [
      ...msgs,
      {
        id: `${Date.now()}_${Math.random()}`,
        sender: "EDEN",
        text: "🎉 Excellent work! Your answer is correct!",
        timestamp: new Date(),
      },
    ]);

    // Update validation state for UI feedback
    setAnswerValidation({
      isCorrect: true,
      message: "Correct! Well done!",
    });

    // Proceed to next question or submit assignment
    setTimeout(() => {
      if (currentQuestionIndex === totalQuestions - 1) {
        submitAssignment();
      } else {
        navigateToQuestion(currentQuestionIndex + 1);
      }
    }, 1500); // Give user time to see the success message
  }

  async function submitAssignment() {
    setIsSubmitting(true);
    await new Promise((res) => setTimeout(res, 1500));
    alert("Assignment Submitted Successfully!");
    setIsSubmitting(false);
    router.push("/student/feedback");
  }

  function navigateToQuestion(index: number) {
    if (!assignment || index >= assignment.problems.length) return;

    setCurrentQuestionIndex(index);
    setCurrentStepInQuestion(0);

    const nextProblem = assignment.problems[index];
    setActiveProblem(nextProblem);

    // Clear validation state for new question
    setAnswerValidation({ isCorrect: null, message: "" });

    // Create new session for this problem
    sessionCreatedRef.current = false;
    createSessionMutation.mutate({ problemId: nextProblem.id });

    setChatMessages([
      {
        id: `${Date.now()}_${Math.random()}`,
        sender: "EDEN",
        text: `Let's work on Question ${index + 1}. ${nextProblem.statement}`,
        timestamp: new Date(),
      },
    ]);
  }

  return (
    <ProtectedRoute>
      <div className={`h-screen w-full bg-gray-50 flex flex-col`}>
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-6 py-2.5 flex items-center justify-start flex-shrink-0">
          <div className="flex items-center gap-4">
            <Link
              href="/student/dashboard"
              className="text-gray-500 hover:text-gray-700"
            >
              <HomeIcon className="h-5 w-5" />
            </Link>
          </div>
          <h1 className="text-lg text-gray-500 text-center chatTitle">
            {assignment.title}
          </h1>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500 mr-4">
              Question {currentQuestionIndex + 1} of {totalQuestions}
            </span>
            <div className="flex items-center gap-1">
              {problems.map((problem, idx) => (
                <button
                  key={problem.id}
                  onClick={() => navigateToQuestion(idx)}
                  className={`
                    w-9 h-9 flex items-center justify-center font-bold text-lg border
                    ${
                      currentQuestionIndex === idx
                        ? "bg-blue-600 text-white scale-110 shadow-lg"
                        : "bg-gray-200 text-gray-700"
                    }
                    ${
                      problem.problem_type === "socratic_conceptual"
                        ? "rounded"
                        : "rounded-full"
                    }
                    border-gray-300 transition-all
                    mx-1
                  `}
                  style={{
                    borderRadius:
                      problem.problem_type === "socratic_conceptual"
                        ? "0.5rem"
                        : "999rem",
                  }}
                  aria-label={`Question ${idx + 1}`}
                >
                  {problem.problem_type === "socratic_conceptual" ? "■" : "●"}
                </button>
              ))}
            </div>
            <div className="flex items-center gap-2 ml-4">
              <button
                onClick={() =>
                  navigateToQuestion(Math.max(0, currentQuestionIndex - 1))
                }
                disabled={currentQuestionIndex === 0}
                className="text-sm text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() =>
                  navigateToQuestion(
                    Math.min(totalQuestions - 1, currentQuestionIndex + 1)
                  )
                }
                disabled={currentQuestionIndex === totalQuestions - 1}
                className="text-sm text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        </header>
        {/* Main Content - Two Panes */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Pane: Completed Steps - Only show for mechanical problems */}
          {shouldShowStepSidebar && (
            <div className="fadeTab showingFade bg-gray-100 border-l border-gray-200 p-3 overflow-y-auto flex-shrink-0">
              <div className="flex items-center gap-2 mb-4">
                <Bars3Icon
                  className="h-5 w-5 cursor-pointer select-none"
                  onClick={() => setShowStepSidebar((prev) => !prev)}
                />
                <CheckIcon className="h-5 w-5 text-green-600" />
                <h3 className="font-medium text-gray-800">Completed Steps</h3>
              </div>
              <p className="text-sm text-gray-500 mb-4">
                {Math.min(currentStepInQuestion, steps.length)} of{" "}
                {steps.length} steps completed
              </p>
              {steps.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-sm text-gray-500">
                    No intermediate steps for this question.
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {steps
                    .slice(0, Math.min(currentStepInQuestion, steps.length))
                    .map((stepText, index) => (
                      <div
                        key={`step_${index}`}
                        className="rounded-lg px-2 py-3 mb-2 border bg-green-50 border-green-200"
                      >
                        <div className="flex items-center gap-2">
                          <CheckIcon className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium text-gray-700">
                            Step {index + 1}: {stepText}
                          </span>
                        </div>
                        {currentProblem &&
                          studentAnswers[currentProblem.id]?.steps[
                            `step_${index}`
                          ] && (
                            <div className="mt-2 pl-6 text-sm text-gray-600">
                              {
                                studentAnswers[currentProblem.id].steps[
                                  `step_${index}`
                                ]
                              }
                            </div>
                          )}
                      </div>
                    ))}
                  {!isFinalAnswerStage &&
                    currentStepInQuestion < steps.length && (
                      <div className="rounded-lg px-2 py-3 mb-2 border bg-gray-50 border-gray-200">
                        <div className="flex items-center gap-2">
                          <ArrowRightIcon className="h-4 w-4 text-gray-600" />
                          <span className="text-sm font-medium text-gray-700">
                            Step {currentStepInQuestion + 1}:{" "}
                            {steps[currentStepInQuestion]}
                          </span>
                        </div>
                      </div>
                    )}
                </div>
              )}
            </div>
          )}
          {/* End Left Pane */}
          {/* Right Pane: Problem, Current Step, Chat */}
          <div className="flex-1 flex flex-col bg-white overflow-hidden">
            {/* Problem Statement Area */}
            <div className="flex flex-row bg-blue-800 px-5 py-2 flex-shrink-0 shadow-lg">
              {/* Problem from Database */}
              <div className="w-full">
                <div className="flex items-center gap-2 mb-1 text-white font-bold">
                  {!shouldShowStepSidebar &&
                    currentProblem?.problem_type === "problem_mechanical" && (
                      <Bars3Icon
                        className="h-5 w-5 cursor-pointer select-none"
                        onClick={() => setShowStepSidebar((prev) => !prev)}
                      />
                    )}
                  {currentProblem ? currentProblem.title : "Loading Problem..."}
                </div>
                {currentProblem && (
                  <div className="bg-gray-50 rounded-lg px-2 py-1.5 mt-1 mb-1 min-h-18">
                    <div className="text-sm color-gray-900 font-medium">
                      {currentProblem.statement}
                    </div>
                  </div>
                )}

                {/* Answer Input Section - Only show for socratic problems */}
                {currentProblem &&
                  currentProblem.problem_type === "socratic_conceptual" && (
                    <div className="mt-2">
                      <div className="bg-white rounded-lg px-3 py-2 flex items-center gap-2">
                        <label className="text-sm font-medium text-gray-700">
                          Your Answer:
                        </label>
                        <input
                          type="text"
                          value={finalAnswer}
                          onChange={(e) => setFinalAnswer(e.target.value)}
                          placeholder="Enter your answer"
                          className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <button
                          onClick={handleAnswerValidation}
                          disabled={!finalAnswer.trim()}
                          className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
                        >
                          Check Answer
                        </button>
                      </div>

                      {/* Answer Validation Result */}
                      {answerValidation.message && (
                        <div
                          className={`mt-2 px-3 py-2 rounded-lg text-sm ${
                            answerValidation.isCorrect === true
                              ? "bg-green-100 text-green-800 border border-green-200"
                              : answerValidation.isCorrect === false
                              ? "bg-red-100 text-red-800 border border-red-200"
                              : "bg-yellow-100 text-yellow-800 border border-yellow-200"
                          }`}
                        >
                          {answerValidation.message}
                        </div>
                      )}
                    </div>
                  )}
              </div>
            </div>

            {/* Chat Messages Area (Scrollable) */}
            <div className="flex-1 overflow-y-auto px-6 py-3">
              <div className="max-w-screen mx-auto space-y-4">
                {chatMessages.map((msg) => (
                  <MessageComponent
                    msg={msg}
                    key={msg.id}
                    firstInitial={user?.email?.charAt(0).toUpperCase()}
                  />
                ))}
                <div ref={chatEndRef} />
              </div>
            </div>

            {/* Answer Input Area */}
            <div className="px-6 py-2 border-t border-gray-200 flex-shrink-0">
              <div className="max-w-screen mx-auto">
                <div className="bg-blue-50 rounded-lg border border-blue-200 p-3 shadow-md">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold text-sm">
                      ✓
                    </div>
                    <span className="font-medium text-gray-800 text-sm">
                      Enter Your Answer
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <input
                      className={`flex-1 px-3 py-2 border rounded-lg focus:ring-1 focus:outline-none text-sm transition-colors ${
                        answerValidation.isCorrect === true
                          ? "border-green-500 bg-green-50 focus:border-green-600 focus:ring-green-500"
                          : answerValidation.isCorrect === false
                          ? "border-red-500 bg-red-50 focus:border-red-600 focus:ring-red-500"
                          : "border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                      }`}
                      placeholder="Enter your answer (e.g., 5 or x = 5)..."
                      value={
                        currentProblem
                          ? studentAnswers[currentProblem.id]?.final[
                              "answer"
                            ] || ""
                          : ""
                      }
                      onChange={(e) => {
                        handleFinalChange("answer", e.target.value);
                        // Clear validation when user starts typing
                        if (answerValidation.message) {
                          setAnswerValidation({ isCorrect: null, message: "" });
                        }
                      }}
                      disabled={isSubmitting}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !isSubmitting) {
                          handleFinalSubmit();
                        }
                      }}
                    />
                    <button
                      className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 text-sm font-medium"
                      onClick={() => {
                        if (
                          currentProblem &&
                          studentAnswers[currentProblem.id]?.final[
                            "answer"
                          ]?.trim()
                        ) {
                          const studentAnswer =
                            studentAnswers[currentProblem.id].final[
                              "answer"
                            ].trim();
                          const expectedAnswer =
                            currentProblem.correct_answer ||
                            currentProblem.microservice_payload?.answer;

                          if (!expectedAnswer) {
                            setAnswerValidation({
                              isCorrect: null,
                              message:
                                "Unable to validate - no expected answer found for this problem.",
                            });
                            return;
                          }

                          const isCorrect = validateAnswer(
                            studentAnswer,
                            expectedAnswer
                          );

                          setAnswerValidation({
                            isCorrect,
                            message: isCorrect
                              ? "Correct! Well done!"
                              : "Incorrect. Please try again.",
                          });
                        }
                      }}
                      disabled={
                        isSubmitting ||
                        !currentProblem ||
                        !studentAnswers[currentProblem.id]?.final[
                          "answer"
                        ]?.trim()
                      }
                    >
                      Check Answer
                    </button>
                    <button
                      className={`px-4 py-2 text-white rounded-lg transition-colors disabled:opacity-50 text-sm font-medium ${
                        answerValidation.isCorrect === true
                          ? "bg-green-600 hover:bg-green-700"
                          : "bg-gray-400 cursor-not-allowed"
                      }`}
                      onClick={handleFinalSubmit}
                      disabled={
                        isSubmitting ||
                        !currentProblem ||
                        !studentAnswers[currentProblem.id]?.final[
                          "answer"
                        ]?.trim() ||
                        answerValidation.isCorrect !== true
                      }
                    >
                      {currentQuestionIndex === totalQuestions - 1
                        ? "Submit Assignment"
                        : "Next Question"}
                    </button>
                  </div>

                  {/* Validation Feedback */}
                  {answerValidation.message && (
                    <div
                      className={`mt-2 px-3 py-2 rounded-lg text-sm ${
                        answerValidation.isCorrect === true
                          ? "bg-green-100 text-green-800 border border-green-200"
                          : answerValidation.isCorrect === false
                          ? "bg-red-100 text-red-800 border border-red-200"
                          : "bg-yellow-100 text-yellow-800 border border-yellow-200"
                      }`}
                    >
                      {answerValidation.message}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Chat Input Area */}
            <div className="px-6 mb-2 flex-shrink-0">
              <div className="max-w-screen mx-auto bg-gray-50 border border-gray-400 rounded-lg px-2 py-2 shadow-md">
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    if (chatInput.trim()) handleChatSend(chatInput);
                  }}
                  className="flex items-center gap-2 mb-1"
                >
                  <input
                    type="text"
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    placeholder="Ask Eden anything..."
                    disabled={chatLoading}
                    className="flex-1 pr-3 pl-1 focus:outline-none rounded-lg"
                  />
                  <button
                    type="submit"
                    disabled={chatLoading || !chatInput.trim()}
                    className="px-2 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors disabled:opacity-50"
                  >
                    <ArrowUpIcon className="h-4.5 w-4.5" />
                  </button>
                </form>
                <div className="flex gap-2 flex-wrap">
                  {quickPrompts.map((prompt) => (
                    <button
                      key={prompt.text}
                      onClick={() => handleChatSend(prompt.text)}
                      disabled={chatLoading}
                      className="px-3 py-1.5 bg-gray-50 border border-gray-200 hover:bg-gray-200 rounded-full text-xs text-gray-600 transition-colors"
                    >
                      <span className="mr-1">{prompt.icon}</span>
                      {prompt.text}
                    </button>
                  ))}
                </div>
              </div>
            </div>
            <p className="text-xs text-gray-500 mb-2 text-center">
              Your conversation with Eden is visible to your teacher. Keep your
              personal information private.
            </p>
          </div>{" "}
          {/* End Right Pane */}
        </div>{" "}
        {/* End Main Content - Two Panes */}
      </div>
    </ProtectedRoute>
  );
}
