// src/app/student/profile/page.tsx
"use client";

import { UserIcon, PencilIcon } from "@heroicons/react/24/outline";

export default function ProfilePage() {
  return (
    <div className="p-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Your Profile
        </h1>
        <p className="text-gray-600">
          Manage your personal information and learning preferences.
        </p>
      </div>

      {/* Coming Soon Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
        <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
          <UserIcon className="h-8 w-8 text-blue-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Profile Management Coming Soon
        </h2>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">
          We're working on a comprehensive profile management system where you'll be able to:
        </p>
        <div className="text-left max-w-md mx-auto space-y-2 mb-8">
          <div className="flex items-center text-gray-600">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
            Edit your personal information
          </div>
          <div className="flex items-center text-gray-600">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
            Update learning preferences
          </div>
          <div className="flex items-center text-gray-600">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
            Manage account settings
          </div>
          <div className="flex items-center text-gray-600">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
            View learning statistics
          </div>
        </div>
        <div className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-500 rounded-lg">
          <PencilIcon className="h-4 w-4 mr-2" />
          Feature in Development
        </div>
      </div>
    </div>
  );
}
