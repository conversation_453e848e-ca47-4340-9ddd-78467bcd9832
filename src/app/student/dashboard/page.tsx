// src/app/student/dashboard/page.tsx
"use client";

import { useAuth } from "@/contexts/auth-context";
import { useIntakeForm } from "@/contexts/intake-form-context";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import RoleBadge from "@/components/role-badge";
import { getUserRole, UserRole } from "@/utils/roles";
import { getStudentProfileData } from "@/app/actions/get-user-profile";
import {
  BookOpenIcon,
  ChartBarIcon,
  UserIcon,
  AcademicCapIcon,
  ClockIcon,
  TrophyIcon,
} from "@heroicons/react/24/outline";

// Define a type for the profile data we expect
interface StudentProfile {
  name: string | null;
  grade: string | null;
  modality_preference: string | null;
  attention_span: string | null;
}

export default function Dashboard() {
  const { user, signOut } = useAuth();
  const { formData } = useIntakeForm(); // formData can still be useful for initial display after intake
  const router = useRouter();
  const searchParams = useSearchParams();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [profileData, setProfileData] = useState<StudentProfile | null>(null); // State for all profile data
  const [isLoadingProfile, setIsLoadingProfile] = useState<boolean>(true); // Start with loading true
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  const [retryCount, setRetryCount] = useState<number>(0);

  // Cache duration: 5 minutes
  const CACHE_DURATION = 5 * 60 * 1000;
  const MAX_RETRIES = 3;

  // Check if coming from intake completion
  const fromIntake = searchParams.get("from") === "intake";

  useEffect(() => {
    async function fetchProfile(forceRefresh = false) {
      if (user) {
        // Check if we have cached data that's still fresh (unless force refresh or coming from intake)
        const now = Date.now();
        if (
          !forceRefresh &&
          !fromIntake &&
          profileData &&
          now - lastFetchTime < CACHE_DURATION
        ) {
          return; // Use cached data
        }

        setIsLoadingProfile(true); // Set loading true when fetch starts

        try {
          const role = await getUserRole();
          setUserRole(role);

          const profileResult = await getStudentProfileData();
          if (profileResult.success && profileResult.data) {
            setProfileData(profileResult.data as StudentProfile);
            setLastFetchTime(now);
            setRetryCount(0); // Reset retry count on success

            // Clear localStorage form data after successful profile load from intake
            if (fromIntake) {
              localStorage.removeItem("edengageIntakeForm");
              // Remove the URL parameter to clean up the URL
              const url = new URL(window.location.href);
              url.searchParams.delete("from");
              window.history.replaceState({}, "", url.toString());
            }
          } else {
            // If profile isn't found in DB, and intake form has some data, use that as a temporary fallback
            // This is mostly relevant if a user just completed intake and lands here before everything is synced
            if (
              formData.name ||
              formData.grade ||
              formData.learning_style ||
              formData.attention_span
            ) {
              // Check if any relevant formData exists
              setProfileData({
                name: formData.name,
                grade: formData.grade,
                modality_preference: formData.learning_style,
                attention_span: formData.attention_span,
              });
              setLastFetchTime(now);
            } else if (retryCount < MAX_RETRIES && fromIntake) {
              // If coming from intake and no data found, retry after a short delay
              console.log(
                `Retrying profile fetch (attempt ${
                  retryCount + 1
                }/${MAX_RETRIES})`
              );
              setRetryCount((prev) => prev + 1);
              setTimeout(() => {
                fetchProfile(true);
              }, 1000 * (retryCount + 1)); // Exponential backoff: 1s, 2s, 3s
              return; // Don't set loading to false yet
            }
            console.error(
              "Failed to fetch student profile for dashboard:",
              profileResult.error
            );
          }
        } catch (error) {
          console.error("Error in fetchProfile:", error);
          if (retryCount < MAX_RETRIES && fromIntake) {
            setRetryCount((prev) => prev + 1);
            setTimeout(() => {
              fetchProfile(true);
            }, 1000 * (retryCount + 1));
            return;
          }
        }

        setIsLoadingProfile(false); // Set loading false after fetch attempt
      }
    }

    if (isLoadingProfile || fromIntake) {
      fetchProfile(fromIntake);
    }
  }, [user, isLoadingProfile, fromIntake, retryCount]); // Include fromIntake and retryCount in dependencies

  const displayName = isLoadingProfile
    ? "Loading..."
    : profileData?.name || user?.email;

  return (
    <div className="p-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome back, {displayName}!
        </h1>
        <p className="text-gray-600">
          Ready to continue your learning journey? Here's what's happening
          today.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <BookOpenIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Active Assignments
              </p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <TrophyIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Average Score</p>
              <p className="text-2xl font-bold text-gray-900">85%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Profile & Quick Actions */}
        <div className="lg:col-span-2 space-y-6">
          {/* Profile Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Your Profile
              </h2>
              {userRole && <RoleBadge role={userRole} />}
            </div>
            {isLoadingProfile ? (
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
              </div>
            ) : profileData ? (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Name</p>
                  <p className="font-medium text-gray-900">
                    {profileData.name || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Grade</p>
                  <p className="font-medium text-gray-900">
                    {profileData.grade || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Learning Style</p>
                  <p className="font-medium text-gray-900">
                    {profileData.modality_preference || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Attention Span</p>
                  <p className="font-medium text-gray-900">
                    {profileData.attention_span || "N/A"}
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-600 mb-4">
                  Could not load profile information. Please complete the intake
                  form if you haven't already.
                </p>
                {fromIntake && (
                  <button
                    onClick={() => {
                      setIsLoadingProfile(true);
                      setRetryCount(0);
                    }}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Retry Loading Profile
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Activity
            </h2>
            <div className="space-y-3">
              <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className="p-2 bg-green-100 rounded-lg mr-3">
                  <AcademicCapIcon className="h-4 w-4 text-green-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    Completed Math Assignment #2
                  </p>
                  <p className="text-xs text-gray-500">2 hours ago</p>
                </div>
              </div>
              <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                  <ClockIcon className="h-4 w-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    Started Science Assignment #1
                  </p>
                  <p className="text-xs text-gray-500">Yesterday</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Quick Actions */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Quick Actions
            </h2>
            <div className="space-y-3">
              <Link
                href="/student/assignments"
                className="flex items-center w-full p-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 hover:bg-blue-100 transition-colors"
              >
                <BookOpenIcon className="h-5 w-5 mr-3" />
                View All Assignments
              </Link>
              <button className="flex items-center w-full p-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-400 cursor-not-allowed">
                <ChartBarIcon className="h-5 w-5 mr-3" />
                View Progress (Coming Soon)
              </button>
              <button className="flex items-center w-full p-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-400 cursor-not-allowed">
                <UserIcon className="h-5 w-5 mr-3" />
                Edit Profile (Coming Soon)
              </button>
            </div>
          </div>

          {/* Upcoming Deadlines */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Upcoming Deadlines
            </h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Math Assignment #3
                  </p>
                  <p className="text-xs text-gray-500">Due in 2 days</p>
                </div>
                <div className="text-yellow-600">
                  <ClockIcon className="h-4 w-4" />
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Science Project
                  </p>
                  <p className="text-xs text-gray-500">Due tomorrow</p>
                </div>
                <div className="text-red-600">
                  <ClockIcon className="h-4 w-4" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Admin/Teacher actions section - only shown to admins and teachers */}
      {userRole && (userRole === "admin" || userRole === "teacher") && (
        <div className="mt-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Administrative Actions
          </h2>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="space-y-4">
              <p className="text-gray-600">
                As a{userRole === "admin" ? "n admin" : " teacher"}, you have
                access to additional features.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link
                  href="/admin/assignments/create"
                  className="block p-4 bg-blue-50 rounded-lg border border-blue-200 text-blue-700 hover:bg-blue-100"
                >
                  Create New Assignment
                </Link>
                {userRole === "admin" && (
                  <Link
                    href="/admin/users"
                    className="block p-4 bg-purple-50 rounded-lg border border-purple-200 text-purple-700 hover:bg-purple-100"
                  >
                    Manage Users
                  </Link>
                )}
                <Link
                  href="/admin/students"
                  className="block p-4 bg-green-50 rounded-lg border border-green-200 text-green-700 hover:bg-green-100"
                >
                  View All Students
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
