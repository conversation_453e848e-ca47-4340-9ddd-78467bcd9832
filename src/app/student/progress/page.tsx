// src/app/student/progress/page.tsx
"use client";

import { ChartBarIcon, TrophyIcon, ClockIcon } from "@heroicons/react/24/outline";

export default function ProgressPage() {
  return (
    <div className="p-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Your Progress
        </h1>
        <p className="text-gray-600">
          Track your learning journey and achievements.
        </p>
      </div>

      {/* Coming Soon Card */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
          <ChartBarIcon className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Progress Tracking Coming Soon
        </h2>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">
          We're building a comprehensive progress tracking system that will include:
        </p>
        <div className="text-left max-w-md mx-auto space-y-2 mb-8">
          <div className="flex items-center text-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            Assignment completion rates
          </div>
          <div className="flex items-center text-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            Learning analytics and insights
          </div>
          <div className="flex items-center text-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            Achievement badges and milestones
          </div>
          <div className="flex items-center text-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            Time spent learning
          </div>
          <div className="flex items-center text-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            Skill development tracking
          </div>
        </div>
        
        {/* Preview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8 mb-8">
          <div className="bg-gray-50 rounded-lg p-4">
            <TrophyIcon className="h-6 w-6 text-yellow-500 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">Achievements</p>
            <p className="text-xs text-gray-500">Coming Soon</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <ClockIcon className="h-6 w-6 text-blue-500 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">Study Time</p>
            <p className="text-xs text-gray-500">Coming Soon</p>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <ChartBarIcon className="h-6 w-6 text-purple-500 mx-auto mb-2" />
            <p className="text-sm font-medium text-gray-700">Performance</p>
            <p className="text-xs text-gray-500">Coming Soon</p>
          </div>
        </div>
        
        <div className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-500 rounded-lg">
          <ChartBarIcon className="h-4 w-4 mr-2" />
          Feature in Development
        </div>
      </div>
    </div>
  );
}
