"use client";

import { useState, useRef, useEffect } from "react";
import Modal from "./ui/modal";

interface TermsAndConditionsProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: (isUnder13: boolean) => void;
  userAge: number;
}

export default function TermsAndConditions({
  isOpen,
  onClose,
  onAccept,
  userAge,
}: TermsAndConditionsProps) {
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const [isUnder13, setIsUnder13] = useState(userAge < 13);
  const [acceptanceChecked, setAcceptanceChecked] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setHasScrolledToBottom(false);
      setAcceptanceChecked(false);
      setIsUnder13(userAge < 13);
    }
  }, [isOpen, userAge]);

  const handleScroll = () => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px tolerance

    if (isAtBottom && !hasScrolledToBottom) {
      setHasScrolledToBottom(true);
    }
  };

  const handleAccept = () => {
    if (acceptanceChecked && hasScrolledToBottom) {
      onAccept(isUnder13);
    }
  };

  const canAccept = hasScrolledToBottom && acceptanceChecked;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="EdEngage Terms of Use and Data Agreement"
      size="xl"
      showCloseButton={false}
    >
      <div className="space-y-4">
        {/* Scrollable Terms Content */}
        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-gray-50"
        >
          <div className="prose prose-sm max-w-none">
            <p className="text-sm text-gray-600 mb-4">
              <strong>Effective Date:</strong> May 25th, 2025
            </p>

            <p className="mb-4">
              Welcome to EdEngage. These Terms of Use and Data Agreement outline
              your rights and responsibilities when using EdEngage outside of a
              school-sponsored setting. By accessing or using EdEngage, you
              agree to the terms outlined below. If you do not agree, you may
              not use the platform.
            </p>

            <h3 className="text-lg font-semibold mt-6 mb-3">
              1. Age Requirement
            </h3>
            <p className="mb-4">
              EdEngage is designed for students aged 10 and older. To use
              EdEngage outside of a school setting, users under the age of 13
              must have verifiable parental consent in accordance with the
              Children's Online Privacy Protection Act (COPPA).
            </p>
            <p className="mb-4">
              If EdEngage becomes aware that a user under 13 has registered
              without verified parental consent, we will immediately delete the
              user's account and all associated data. This practice aligns with
              COPPA and standard industry procedures.
            </p>

            <h3 className="text-lg font-semibold mt-6 mb-3">
              2. Data Collection and Use
            </h3>
            <p className="mb-4">
              When using EdEngage, we may collect the following types of data:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>
                Personal identifiers such as your name, email address, and age
              </li>
              <li>Demographic information voluntarily provided by you</li>
              <li>Chat interactions between you and the EdEngage platform</li>
            </ul>
            <p className="mb-4">We use this information to:</p>
            <ul className="list-disc pl-6 mb-4">
              <li>Deliver a personalized learning experience</li>
              <li>
                Improve the quality and responsiveness of our large language
                model (LLM)
              </li>
              <li>Monitor platform performance and user engagement</li>
            </ul>
            <p className="mb-4">
              EdEngage is committed to transparency and provides clear
              information on how your data is collected, used, and stored. No
              data will be sold to third parties.
            </p>

            <h3 className="text-lg font-semibold mt-6 mb-3">
              3. Use of De-Identified Data for AI Training
            </h3>
            <p className="mb-4">
              To improve the educational effectiveness of our AI tutor, EdEngage
              may use your chat history and interactions in a de-identified
              format for training its large language model.
            </p>
            <p className="mb-4">
              All personally identifiable information (PII), including names,
              emails, and indirect identifiers, will be removed prior to use.
              Once data is de-identified, it is no longer subject to FERPA or
              similar education privacy laws but remains governed by our
              internal privacy standards.
            </p>

            <h3 className="text-lg font-semibold mt-6 mb-3">
              4. Data Access and Deletion Rights
            </h3>
            <p className="mb-4">
              Users have the right to request the deletion of their data at any
              time.
            </p>
            <p className="mb-4">
              To request deletion or review of your personal data, please
              contact <EMAIL>. Upon receiving a valid request,
              EdEngage will promptly delete your data from our systems unless
              retention is required by law or for operational integrity.
            </p>

            <h3 className="text-lg font-semibold mt-6 mb-3">
              5. FERPA Applicability
            </h3>
            <p className="mb-4">
              When EdEngage is used outside of a school setting, the Family
              Educational Rights and Privacy Act (FERPA) does not apply.
              However, we voluntarily adhere to strong data privacy standards
              that align with FERPA principles, including:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>
                Limiting data collection to what is necessary for educational
                purposes
              </li>
              <li>Offering users access to their own data upon request</li>
              <li>Avoiding disclosure of personal data without user consent</li>
            </ul>

            <h3 className="text-lg font-semibold mt-6 mb-3">
              6. Updates to Terms
            </h3>
            <p className="mb-4">
              EdEngage may occasionally update these terms to reflect
              improvements to the platform or to comply with applicable laws.
              You will be notified of any material changes. Continued use of
              EdEngage after such notice constitutes your acceptance of the
              revised terms.
            </p>

            <h3 className="text-lg font-semibold mt-6 mb-3">7. Contact Us</h3>
            <p className="mb-4">
              For questions about these Terms or our data practices, you may
              contact EdEngage CEO, Mick Tobin at: <EMAIL>
            </p>
          </div>
        </div>

        {/* Scroll indicator */}
        {!hasScrolledToBottom && (
          <p className="text-sm text-orange-600 font-medium">
            Please scroll to the bottom to read all terms before accepting.
          </p>
        )}

        {/* Age-based acceptance checkbox */}
        {hasScrolledToBottom && (
          <div className="space-y-3">
            <label className="flex items-start gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={acceptanceChecked}
                onChange={(e) => setAcceptanceChecked(e.target.checked)}
                className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                {isUnder13 ? (
                  <>
                    <strong>
                      I am the parent or legal guardian of the child, and I give
                      my verifiable consent for them to use EdEngage.
                    </strong>{" "}
                    I have reviewed and accept the Terms of Use and Privacy
                    Policy.
                  </>
                ) : (
                  <>
                    <strong>
                      By checking this box, I confirm that I am at least 13
                      years old and accept the Terms and Conditions.
                    </strong>
                  </>
                )}
              </span>
            </label>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleAccept}
            disabled={!canAccept}
            className={`px-6 py-2 rounded-md font-medium ${
              canAccept
                ? "bg-blue-600 text-white hover:bg-blue-700"
                : "bg-gray-300 text-gray-500 cursor-not-allowed"
            }`}
          >
            Accept and Continue
          </button>
        </div>
      </div>
    </Modal>
  );
}
