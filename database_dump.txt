-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.ai_performance_metrics (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  endpoint text NOT NULL,
  response_time_ms integer NOT NULL,
  token_usage jsonb DEFAULT '{}'::jsonb,
  cache_hit boolean DEFAULT false,
  user_id uuid,
  session_id uuid,
  timestamp timestamp with time zone DEFAULT now(),
  CONSTRAINT ai_performance_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT ai_performance_metrics_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT ai_performance_metrics_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.tutoring_sessions(id)
);
CREATE TABLE public.ai_response_cache (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  cache_key text NOT NULL UNIQUE,
  endpoint text NOT NULL,
  request_payload jsonb NOT NULL,
  response_payload jsonb NOT NULL,
  hit_count integer DEFAULT 0,
  ttl_seconds integer DEFAULT 300,
  created_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone NOT NULL,
  CONSTRAINT ai_response_cache_pkey PRIMARY KEY (id)
);
CREATE TABLE public.assignment_problems (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  student_assignment_id uuid NOT NULL,
  problem_id uuid NOT NULL,
  sequence_order integer NOT NULL,
  problem_type_override text,
  status text DEFAULT 'not_started'::text CHECK (status = ANY (ARRAY['not_started'::text, 'in_progress'::text, 'completed'::text, 'skipped'::text])),
  score numeric,
  attempts_count integer DEFAULT 0,
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  CONSTRAINT assignment_problems_pkey PRIMARY KEY (id),
  CONSTRAINT assignment_problems_student_assignment_id_fkey FOREIGN KEY (student_assignment_id) REFERENCES public.student_assignments(id),
  CONSTRAINT assignment_problems_problem_id_fkey FOREIGN KEY (problem_id) REFERENCES public.problems(id)
);
CREATE TABLE public.assignments (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  title text NOT NULL,
  description text,
  assignment_type text DEFAULT 'practice'::text CHECK (assignment_type = ANY (ARRAY['homework'::text, 'quiz'::text, 'practice'::text, 'assessment'::text])),
  time_limit_minutes integer,
  randomize_problems boolean DEFAULT false,
  socratic_mode_enabled boolean DEFAULT true,
  created_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT assignments_pkey PRIMARY KEY (id),
  CONSTRAINT assignments_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.chat_messages (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  session_id uuid NOT NULL,
  role text NOT NULL CHECK (role = ANY (ARRAY['student'::text, 'assistant'::text, 'system'::text])),
  content text NOT NULL,
  message_type text DEFAULT 'dialogue'::text CHECK (message_type = ANY (ARRAY['dialogue'::text, 'hint'::text, 'feedback'::text, 'step_check'::text, 'system_note'::text])),
  step_context_id uuid,
  mode_when_sent text,
  agent_outputs jsonb DEFAULT '{}'::jsonb,
  profile_updates_suggested jsonb DEFAULT '{}'::jsonb,
  retrieved_materials jsonb DEFAULT '[]'::jsonb,
  understanding_score numeric,
  engagement_indicator text CHECK (engagement_indicator = ANY (ARRAY['high'::text, 'medium'::text, 'low'::text, 'disengaged'::text])),
  misconceptions_flagged jsonb DEFAULT '[]'::jsonb,
  timestamp timestamp with time zone DEFAULT now(),
  sender_type text NOT NULL DEFAULT ''::text,
  problem_id uuid,
  student_vote boolean,
  downvote_description text,
  CONSTRAINT chat_messages_pkey PRIMARY KEY (id),
  CONSTRAINT chat_messages_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.tutoring_sessions(id),
  CONSTRAINT chat_messages_step_context_id_fkey FOREIGN KEY (step_context_id) REFERENCES public.problem_steps(id),
  CONSTRAINT fk_problem_id FOREIGN KEY (problem_id) REFERENCES public.problems(id)
);
CREATE TABLE public.chat_responses (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  session_id uuid NOT NULL,
  message_id uuid NOT NULL,
  reply text NOT NULL,
  moderation_flagged boolean DEFAULT false,
  moderation_response text,
  agent_outputs jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT chat_responses_pkey PRIMARY KEY (id),
  CONSTRAINT chat_responses_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.tutoring_sessions(id),
  CONSTRAINT chat_responses_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.chat_messages(id)
);
CREATE TABLE public.learning_analytics (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  analysis_type text CHECK (analysis_type = ANY (ARRAY['session'::text, 'assignment'::text, 'weekly'::text, 'unit'::text])),
  reference_id uuid,
  time_period_start date,
  time_period_end date,
  strengths jsonb DEFAULT '[]'::jsonb,
  areas_for_improvement jsonb DEFAULT '[]'::jsonb,
  misconceptions_identified jsonb DEFAULT '[]'::jsonb,
  learning_patterns jsonb DEFAULT '{}'::jsonb,
  recommendations jsonb DEFAULT '[]'::jsonb,
  engagement_score numeric CHECK (engagement_score >= 0::numeric AND engagement_score <= 1::numeric),
  understanding_score numeric CHECK (understanding_score >= 0::numeric AND understanding_score <= 1::numeric),
  persistence_score numeric CHECK (persistence_score >= 0::numeric AND persistence_score <= 1::numeric),
  growth_indicator text CHECK (growth_indicator = ANY (ARRAY['declining'::text, 'stable'::text, 'improving'::text, 'accelerating'::text])),
  ai_confidence numeric,
  ai_model_version text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT learning_analytics_pkey PRIMARY KEY (id),
  CONSTRAINT learning_analytics_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.material_embeddings (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  material_id uuid NOT NULL,
  content_chunk text NOT NULL,
  chunk_index integer DEFAULT 0,
  embedding USER-DEFINED,
  embedding_model text DEFAULT 'text-embedding-ada-002'::text,
  chunk_metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT material_embeddings_pkey PRIMARY KEY (id),
  CONSTRAINT material_embeddings_material_id_fkey FOREIGN KEY (material_id) REFERENCES public.materials(id)
);
CREATE TABLE public.materials (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  title text NOT NULL,
  content text NOT NULL,
  material_type text CHECK (material_type = ANY (ARRAY['teacher_note'::text, 'lesson'::text, 'example'::text, 'definition'::text, 'video_transcript'::text])),
  standard_codes ARRAY DEFAULT '{}'::text[],
  grade_levels ARRAY DEFAULT '{}'::integer[],
  topics ARRAY DEFAULT '{}'::text[],
  difficulty_level integer,
  source_url text,
  file_metadata jsonb DEFAULT '{}'::jsonb,
  retrieval_count integer DEFAULT 0,
  helpfulness_rating numeric,
  uploaded_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT materials_pkey PRIMARY KEY (id),
  CONSTRAINT materials_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.users(id)
);
CREATE TABLE public.math_standards (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  code text NOT NULL UNIQUE,
  title text NOT NULL,
  description text,
  grade_level integer NOT NULL,
  domain text,
  cluster text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT math_standards_pkey PRIMARY KEY (id)
);
CREATE TABLE public.problem_steps (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  problem_id uuid NOT NULL,
  step_number integer NOT NULL,
  step_description text NOT NULL,
  step_type text CHECK (step_type = ANY (ARRAY['setup'::text, 'calculation'::text, 'verification'::text, 'reflection'::text])),
  expected_input text,
  validation_criteria jsonb DEFAULT '{}'::jsonb,
  hints_for_step jsonb DEFAULT '[]'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT problem_steps_pkey PRIMARY KEY (id),
  CONSTRAINT problem_steps_problem_id_fkey FOREIGN KEY (problem_id) REFERENCES public.problems(id)
);
CREATE TABLE public.problems (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  title text,
  statement text NOT NULL,
  problem_type text NOT NULL,
  standard_code text,
  difficulty_level integer DEFAULT 5 CHECK (difficulty_level >= 1 AND difficulty_level <= 10),
  grade_level integer,
  correct_answer jsonb,
  solution_approach text,
  expected_steps jsonb DEFAULT '[]'::jsonb,
  hints jsonb DEFAULT '[]'::jsonb,
  ai_generated boolean DEFAULT false,
  generation_metadata jsonb DEFAULT '{}'::jsonb,
  personalization_tags ARRAY DEFAULT '{}'::text[],
  times_used integer DEFAULT 0,
  avg_completion_time_minutes numeric,
  success_rate numeric,
  created_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  microservice_payload jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT problems_pkey PRIMARY KEY (id),
  CONSTRAINT problems_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.profile_update_log (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  source_endpoint text NOT NULL,
  source_session_id uuid,
  field_path text NOT NULL,
  old_value jsonb,
  new_value jsonb NOT NULL,
  update_type text CHECK (update_type = ANY (ARRAY['increment'::text, 'decrement'::text, 'set'::text, 'append'::text, 'remove'::text])),
  ai_confidence numeric,
  auto_applied boolean DEFAULT false,
  applied_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT profile_update_log_pkey PRIMARY KEY (id),
  CONSTRAINT profile_update_log_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT profile_update_log_source_session_id_fkey FOREIGN KEY (source_session_id) REFERENCES public.tutoring_sessions(id)
);
CREATE TABLE public.socratic_responses (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  session_id uuid NOT NULL,
  step_work_id uuid,
  reply text NOT NULL,
  current_step integer,
  steps_covered ARRAY,
  next_expected integer,
  all_steps_sufficient boolean DEFAULT false,
  step_scores jsonb DEFAULT '[]'::jsonb,
  agent_outputs jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT socratic_responses_pkey PRIMARY KEY (id),
  CONSTRAINT socratic_responses_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.tutoring_sessions(id),
  CONSTRAINT socratic_responses_step_work_id_fkey FOREIGN KEY (step_work_id) REFERENCES public.student_step_work(id)
);
CREATE TABLE public.student_affective_profiles (
  user_id uuid NOT NULL,
  interest_level integer DEFAULT 3 CHECK (interest_level >= 1 AND interest_level <= 5),
  self_efficacy_score numeric DEFAULT 0.5 CHECK (self_efficacy_score >= 0::numeric AND self_efficacy_score <= 1::numeric),
  goal_orientation text DEFAULT 'mastery'::text CHECK (goal_orientation = ANY (ARRAY['mastery'::text, 'performance'::text, 'mixed'::text])),
  mindset text DEFAULT 'growth'::text CHECK (mindset = ANY (ARRAY['growth'::text, 'fixed'::text, 'mixed'::text])),
  math_anxiety_level text DEFAULT 'moderate'::text CHECK (math_anxiety_level = ANY (ARRAY['low'::text, 'moderate'::text, 'high'::text])),
  reaction_to_failure text,
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT student_affective_profiles_pkey PRIMARY KEY (user_id),
  CONSTRAINT student_affective_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.student_assignments (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  assignment_id uuid NOT NULL,
  status text DEFAULT 'not_started'::text CHECK (status = ANY (ARRAY['not_started'::text, 'in_progress'::text, 'completed'::text, 'abandoned'::text])),
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  total_time_minutes integer,
  final_score numeric,
  session_summary jsonb,
  teacher_insights jsonb,
  profile_updates_applied jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT student_assignments_pkey PRIMARY KEY (id),
  CONSTRAINT student_assignments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT student_assignments_assignment_id_fkey FOREIGN KEY (assignment_id) REFERENCES public.assignments(id)
);
CREATE TABLE public.student_explanation_preferences (
  id integer NOT NULL DEFAULT nextval('student_explanation_preferences_id_seq'::regclass),
  user_id uuid NOT NULL,
  method_name text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT student_explanation_preferences_pkey PRIMARY KEY (id),
  CONSTRAINT student_explanation_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.student_feedback (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  email text,
  q1 smallint NOT NULL CHECK (q1 > 0 AND q1 < 6),
  q2 smallint NOT NULL CHECK (q2 > 0 AND q2 < 6),
  q3 text,
  q4 text,
  CONSTRAINT student_feedback_pkey PRIMARY KEY (id)
);
CREATE TABLE public.student_interests (
  id integer NOT NULL DEFAULT nextval('student_interests_id_seq'::regclass),
  user_id uuid NOT NULL,
  interest_name text NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT student_interests_pkey PRIMARY KEY (id),
  CONSTRAINT student_interests_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.student_mastery (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  standard_code text NOT NULL,
  mastery_level numeric NOT NULL DEFAULT 0.0 CHECK (mastery_level >= 0::numeric AND mastery_level <= 1::numeric),
  confidence_level numeric DEFAULT 0.5 CHECK (confidence_level >= 0::numeric AND confidence_level <= 1::numeric),
  attempts_count integer DEFAULT 0,
  correct_count integer DEFAULT 0,
  last_practiced timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT student_mastery_pkey PRIMARY KEY (id),
  CONSTRAINT student_mastery_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.student_misconceptions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  concept text NOT NULL,
  misconception_description text NOT NULL,
  confidence_score numeric DEFAULT 0.5,
  first_observed timestamp with time zone DEFAULT now(),
  last_observed timestamp with time zone DEFAULT now(),
  times_observed integer DEFAULT 1,
  status text DEFAULT 'active'::text CHECK (status = ANY (ARRAY['active'::text, 'resolved'::text, 'improving'::text])),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT student_misconceptions_pkey PRIMARY KEY (id),
  CONSTRAINT student_misconceptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.student_profiles (
  user_id uuid NOT NULL,
  name text,
  grade text,
  attention_span text DEFAULT 'medium'::text CHECK (attention_span = ANY (ARRAY['short'::text, 'medium'::text, 'long'::text])),
  engagement_level text DEFAULT 'moderate'::text,
  modality_preference text DEFAULT 'mixed'::text CHECK (modality_preference = ANY (ARRAY['visual'::text, 'auditory'::text, 'kinesthetic'::text, 'mixed'::text])),
  challenge_preference text DEFAULT 'moderate'::text CHECK (challenge_preference = ANY (ARRAY['easy'::text, 'moderate'::text, 'difficult'::text, 'applied'::text])),
  tutoring_style_preference text DEFAULT 'socratic'::text CHECK (tutoring_style_preference = ANY (ARRAY['socratic'::text, 'direct'::text, 'step_by_step'::text])),
  optimal_difficulty_level integer DEFAULT 5 CHECK (optimal_difficulty_level >= 1 AND optimal_difficulty_level <= 10),
  collaboration_preference text,
  communication_style text,
  self_assessment_ability text,
  global_correct_first_try_rate numeric DEFAULT 0.0,
  global_skip_rate numeric DEFAULT 0.0,
  ai_learning_patterns jsonb DEFAULT '{}'::jsonb,
  ai_recommendations jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  age integer DEFAULT 0,
  languages ARRAY DEFAULT '{English}'::text[],
  CONSTRAINT student_profiles_pkey PRIMARY KEY (user_id),
  CONSTRAINT student_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.student_step_work (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  session_id uuid NOT NULL,
  step_id uuid NOT NULL,
  attempt_number integer DEFAULT 1,
  student_response text,
  explanation_provided text,
  steps_covered jsonb DEFAULT '[]'::jsonb,
  current_step_status text DEFAULT 'in_progress'::text CHECK (current_step_status = ANY (ARRAY['not_started'::text, 'in_progress'::text, 'completed'::text, 'needs_review'::text])),
  next_expected_step text,
  ai_feedback text,
  socratic_response text,
  understanding_score numeric,
  completeness_score numeric,
  is_correct boolean,
  time_spent_seconds integer,
  submitted_at timestamp with time zone DEFAULT now(),
  CONSTRAINT student_step_work_pkey PRIMARY KEY (id),
  CONSTRAINT student_step_work_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.tutoring_sessions(id),
  CONSTRAINT student_step_work_step_id_fkey FOREIGN KEY (step_id) REFERENCES public.problem_steps(id)
);
CREATE TABLE public.student_strategies (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  strategy_name text NOT NULL,
  effectiveness_score numeric,
  usage_frequency integer DEFAULT 1,
  last_observed timestamp with time zone DEFAULT now(),
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT student_strategies_pkey PRIMARY KEY (id),
  CONSTRAINT student_strategies_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.teacher_student_notes (
  user_id uuid NOT NULL,
  teacher_notes text,
  reading_level text,
  ell_status boolean DEFAULT false,
  accommodations ARRAY,
  learning_environment_needs ARRAY,
  last_updated_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT teacher_student_notes_pkey PRIMARY KEY (user_id),
  CONSTRAINT teacher_student_notes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT teacher_student_notes_last_updated_by_fkey FOREIGN KEY (last_updated_by) REFERENCES public.users(id)
);
CREATE TABLE public.tutoring_sessions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  assignment_problem_id uuid,
  user_id uuid NOT NULL,
  current_mode text DEFAULT 'socratic_dialogue'::text CHECK (current_mode = ANY (ARRAY['socratic_dialogue'::text, 'mechanical_steps'::text, 'final_answer_check'::text])),
  current_step_id uuid,
  status text DEFAULT 'active'::text CHECK (status = ANY (ARRAY['active'::text, 'completed'::text, 'paused'::text])),
  started_at timestamp with time zone DEFAULT now(),
  last_activity_at timestamp with time zone DEFAULT now(),
  ended_at timestamp with time zone,
  total_messages integer DEFAULT 0,
  student_message_count integer DEFAULT 0,
  engagement_score numeric,
  understanding_score numeric,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  session_metadata jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT tutoring_sessions_pkey PRIMARY KEY (id),
  CONSTRAINT tutoring_sessions_assignment_problem_id_fkey FOREIGN KEY (assignment_problem_id) REFERENCES public.assignment_problems(id),
  CONSTRAINT tutoring_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT tutoring_sessions_current_step_id_fkey FOREIGN KEY (current_step_id) REFERENCES public.problem_steps(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL,
  email text NOT NULL UNIQUE,
  full_name text,
  role text NOT NULL DEFAULT 'student'::text CHECK (role = ANY (ARRAY['student'::text, 'teacher'::text, 'admin'::text])),
  school_id uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT users_pkey PRIMARY KEY (id),
  CONSTRAINT users_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id)
);