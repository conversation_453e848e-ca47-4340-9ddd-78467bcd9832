-- Assignment System Schema Updates
-- Run this in your Supabase SQL editor

-- 1. Update problems table to support multiple standards
ALTER TABLE problems 
ALTER COLUMN standard_code TYPE TEXT[] USING ARRAY[standard_code];

-- 2. Add target_standard and is_template fields to assignments table
ALTER TABLE assignments 
ADD COLUMN IF NOT EXISTS target_standard TEXT,
ADD COLUMN IF NOT EXISTS is_template BOOLEAN DEFAULT false;

-- 3. Create index for efficient standard_code queries
CREATE INDEX IF NOT EXISTS idx_problems_standard_code_gin ON problems USING GIN(standard_code);

-- 4. Insert sample problems for expressions_equations MVP

-- Q1: Fixed mechanical problem (same for all students)
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '11111111-1111-1111-1111-111111111111',
    'Q1: Basic Linear Equation',
    'Solve for x: 3x + 7 = 22',
    'problem_mechanical',
    ARRAY['expressions_equations'],
    3,
    '[
        "Subtract 7 from both sides: 3x = 15",
        "Divide by 3: x = 5"
    ]',
    '{"x": "5"}',
    '{"answer": "x = 5", "variables": ["x"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- Q3: Fixed socratic problem (same for all students)
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '33333333-3333-3333-3333-333333333333',
    'Q3: System of Equations Analysis',
    'Solve the system: 2x + y = 8 and x - y = 1. Explain your reasoning at each step.',
    'socratic_conceptual',
    ARRAY['expressions_equations'],
    6,
    '[
        "Choose a method (substitution or elimination)",
        "Apply the chosen method to eliminate one variable",
        "Solve for the remaining variable",
        "Substitute back to find the other variable",
        "Verify your solution"
    ]',
    '{"x": "3", "y": "2"}',
    '{"answer": "x = 3, y = 2", "variables": ["x", "y"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- Q2 Options: Personalized mechanical problems (different combinations)

-- expressions_equations + number_system
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '22222222-2222-2222-2222-222222222223',
    'Q2: Number System & Equations',
    'A number added to its square root equals 12. What is the number?',
    'problem_mechanical',
    ARRAY['expressions_equations', 'number_system'],
    7,
    '[
        "Let x be the number.",
        "Write the equation: x + √x = 12",
        "Try values: x = 9 → 9 + √9 = 9 + 3 = 12",
        "Verify: 9 is correct."
    ]',
    '{"answer": "9"}',
    '{"answer": "9", "variables": ["x"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- expressions_equations + geometry
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '22222222-2222-2222-2222-222222222224',
    'Q2: Geometry & Equations',
    'The perimeter of a rectangle is 42 cm. The length is 3 cm more than twice the width. What is the width?',
    'problem_mechanical',
    ARRAY['expressions_equations', 'geometry'],
    6,
    '[
        "Let x be the width.",
        "Then the length is 2x + 3.",
        "Perimeter formula: 2(length + width) = 42",
        "Set up equation: 2((2x + 3) + x) = 42",
        "Simplify: 2(3x + 3) = 42 → 6x + 6 = 42",
        "Solve: 6x = 36 → x = 6"
    ]',
    '{"answer": "6"}',
    '{"answer": "6 cm", "variables": ["width"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- expressions_equations + functions
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '22222222-2222-2222-2222-222222222225',
    'Q2: Functions & Equations',
    'A function triples an input number and adds 4. If the output is 25, what was the input?',
    'problem_mechanical',
    ARRAY['expressions_equations', 'functions'],
    5,
    '[
        "Let x be the input.",
        "Function rule: 3x + 4 = 25",
        "Solve: 3x = 21 → x = 7"
    ]',
    '{"answer": "7"}',
    '{"answer": "7", "variables": ["x"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- expressions_equations + statistics_probability
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '22222222-2222-2222-2222-222222222226',
    'Q2: Statistics & Equations',
    'The average of five test scores is 80. Four of the scores are 75, 82, 79, and 88. What is the fifth score?',
    'problem_mechanical',
    ARRAY['expressions_equations', 'statistics_probability'],
    6,
    '[
        "Let x be the fifth score.",
        "Mean formula: (75 + 82 + 79 + 88 + x)/5 = 80",
        "Add known scores: 324 + x = 400",
        "Solve: x = 76"
    ]',
    '{"answer": "76"}',
    '{"answer": "76", "variables": ["score"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- expressions_equations + expressions_equations (fallback)
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES (
    '22222222-2222-2222-2222-222222222222',
    'Q2: Advanced Expressions & Equations',
    'Solve for x: 3(2x - 4) = 2(x + 5) + x',
    'problem_mechanical',
    ARRAY['expressions_equations', 'expressions_equations'],
    7,
    '[
        "Expand both sides: 6x - 12 = 2x + 10 + x",
        "Combine like terms: 6x - 12 = 3x + 10",
        "Subtract 3x from both sides: 3x - 12 = 10",
        "Add 12: 3x = 22",
        "Solve: x = 6"
    ]',
    '{"answer": "6"}',
    '{"answer": "x = 6", "variables": ["x"]}'
) ON CONFLICT (id) DO UPDATE SET
    standard_code = EXCLUDED.standard_code,
    problem_type = EXCLUDED.problem_type;

-- Create template assignment
INSERT INTO assignments (
    id,
    title,
    description,
    assignment_type,
    target_standard,
    is_template,
    socratic_mode_enabled,
    created_by
) VALUES (
    '550e8400-e29b-41d4-a716-446655440000',
    'Expressions & Equations Practice',
    'Practice solving equations and working with expressions. This assignment adapts to your learning needs.',
    'practice',
    'expressions_equations',
    true,
    true,
    (SELECT id FROM users WHERE role = 'teacher' LIMIT 1)
) ON CONFLICT (id) DO UPDATE SET
    target_standard = EXCLUDED.target_standard,
    is_template = EXCLUDED.is_template;

-- Verify the data
SELECT 
    title,
    problem_type,
    standard_code,
    array_length(standard_code, 1) as standard_count
FROM problems 
WHERE standard_code && ARRAY['expressions_equations']
ORDER BY problem_type, array_length(standard_code, 1);
